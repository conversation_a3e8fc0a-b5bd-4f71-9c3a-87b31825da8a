
import FormInput from './FormInput';
import FormSection from './FormSection';
import CheckboxGroup from './CheckboxGroup';
import FormTextarea from './FormTextarea';
import { CONTACT_REASONS} from './constants';

export default function Form() {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
    console.log('Form submitted');
  };

  return (
    <form
      onSubmit={handleSubmit}
      className=" bg-white dark:bg-black text-black dark:text-white w-full p-5 rounded-2xl h-full space-y-2"
    >
      {/* Personal Information Row */}
      <div className="flex gap-4 w-full">
        <FormInput
          id="fullName"
          label="Full Name"
          type="text"
          required
          className="w-1/2"
        />
        <FormInput
          id="email"
          label="Email"
          type="email"
          required
          className="w-1/2"
        />
      </div>

      {/* Contact Reasons Section */}
      <FormSection title="Why are you contacting us?">
        <CheckboxGroup
          options={CONTACT_REASONS}
          name="contactReasons"
        />
      </FormSection>


      {/* Message Section */}
      <FormSection>
        <FormTextarea
          id="message"
          label="Your Message"
          rows={3}
          required
        />
      </FormSection>

      {/* Submit Button */}
        <div className='w-full  text-white  flex items-center justify-center gap-2.5'>
            <button className=' w-1/4 bg-gradient-to-r rounded-full py-2.5 px-6 from-[#722973] to-[#a43ca6] text-base font-medium'>Send</button>
        </div>
    </form>
  );
}
